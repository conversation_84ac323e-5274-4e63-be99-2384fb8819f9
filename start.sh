#!/bin/bash

# Legal Assistant - Multi-Agent System Startup Script
# This script starts both backend and frontend servers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_colored() {
    echo -e "${1}${2}${NC}"
}

# Function to print header
print_header() {
    echo
    print_colored $PURPLE "============================================================"
    print_colored $PURPLE "🏛️  Legal Assistant - Multi-Agent System"
    print_colored $PURPLE "============================================================"
    echo
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to cleanup processes on exit
cleanup() {
    print_colored $YELLOW "\n🛑 Shutting down services..."
    
    # Kill background processes
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_colored $GREEN "✅ Backend stopped"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_colored $GREEN "✅ Frontend stopped"
    fi
    
    print_colored $GREEN "✅ All services stopped"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Print header
print_header

# Check dependencies
print_colored $BLUE "🔍 Checking dependencies..."

if ! command_exists uv; then
    print_colored $RED "❌ uv is not installed. Please install uv first."
    print_colored $RED "   Visit: https://docs.astral.sh/uv/getting-started/installation/"
    exit 1
fi
print_colored $GREEN "✅ uv is available"

if ! command_exists npm; then
    print_colored $RED "❌ npm is not installed. Please install Node.js and npm first."
    exit 1
fi
print_colored $GREEN "✅ npm is available"

# Check if frontend dependencies are installed
if [ ! -d "frontend/node_modules" ]; then
    print_colored $YELLOW "📦 Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    print_colored $GREEN "✅ Frontend dependencies installed"
else
    print_colored $GREEN "✅ Frontend dependencies are installed"
fi

echo

# Start backend
print_colored $BLUE "🚀 Starting backend server (FastAPI)..."
uv run fastapi run app --port 8001 --reload > backend.log 2>&1 &
BACKEND_PID=$!
print_colored $GREEN "✅ Backend server started on http://localhost:8001 (PID: $BACKEND_PID)"

# Wait a moment for backend to start
sleep 3

# Start frontend
print_colored $BLUE "🚀 Starting frontend server (Next.js)..."
cd frontend
npm run dev > ../frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..
print_colored $GREEN "✅ Frontend server started on http://localhost:3000 (PID: $FRONTEND_PID)"

# Wait for servers to be ready
print_colored $YELLOW "⏳ Waiting for servers to be ready..."
sleep 5

# Display access information
echo
print_colored $GREEN "🎉 Application is ready!"
echo
print_colored $BLUE "📱 Frontend: http://localhost:3000"
print_colored $BLUE "🔧 Backend API: http://localhost:8001"
print_colored $BLUE "📚 API Docs: http://localhost:8001/docs"
print_colored $BLUE "🤖 Agent Health: http://localhost:8001/v1/agents/health"
echo
print_colored $YELLOW "📋 Logs:"
print_colored $YELLOW "   Backend: tail -f backend.log"
print_colored $YELLOW "   Frontend: tail -f frontend.log"
echo
print_colored $YELLOW "Press Ctrl+C to stop all services"

# Keep script running and monitor processes
while true; do
    # Check if backend is still running
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        print_colored $RED "❌ Backend process died unexpectedly"
        cleanup
    fi
    
    # Check if frontend is still running
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        print_colored $RED "❌ Frontend process died unexpectedly"
        cleanup
    fi
    
    sleep 2
done
