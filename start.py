#!/usr/bin/env python3
"""
Startup script for the Legal Assistant application.

This script starts both the backend (FastAPI) and frontend (Next.js) servers
in development mode with proper process management and logging.
"""

import os
import sys
import subprocess
import signal
import time
import threading
from pathlib import Path

# Color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_colored(message, color=Colors.OKBLUE):
    """Print colored message to terminal."""
    print(f"{color}{message}{Colors.ENDC}")

def print_header():
    """Print application header."""
    print_colored("=" * 60, Colors.HEADER)
    print_colored("🏛️  Legal Assistant - Multi-Agent System", Colors.HEADER)
    print_colored("=" * 60, Colors.HEADER)
    print()

class ProcessManager:
    """Manages backend and frontend processes."""
    
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        print_colored("\n🛑 Shutdown signal received. Stopping services...", Colors.WARNING)
        self.stop_all()
        sys.exit(0)
    
    def check_dependencies(self):
        """Check if required dependencies are available."""
        print_colored("🔍 Checking dependencies...", Colors.OKBLUE)
        
        # Check if uv is available
        try:
            subprocess.run(["uv", "--version"], check=True, capture_output=True)
            print_colored("✅ uv is available", Colors.OKGREEN)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print_colored("❌ uv is not installed. Please install uv first.", Colors.FAIL)
            print_colored("   Visit: https://docs.astral.sh/uv/getting-started/installation/", Colors.FAIL)
            return False
        
        # Check if npm is available
        try:
            subprocess.run(["npm", "--version"], check=True, capture_output=True)
            print_colored("✅ npm is available", Colors.OKGREEN)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print_colored("❌ npm is not installed. Please install Node.js and npm first.", Colors.FAIL)
            return False
        
        # Check if frontend dependencies are installed
        frontend_dir = Path("frontend")
        if not (frontend_dir / "node_modules").exists():
            print_colored("📦 Installing frontend dependencies...", Colors.WARNING)
            try:
                subprocess.run(
                    ["npm", "install"], 
                    cwd=frontend_dir, 
                    check=True,
                    capture_output=True
                )
                print_colored("✅ Frontend dependencies installed", Colors.OKGREEN)
            except subprocess.CalledProcessError as e:
                print_colored(f"❌ Failed to install frontend dependencies: {e}", Colors.FAIL)
                return False
        else:
            print_colored("✅ Frontend dependencies are installed", Colors.OKGREEN)
        
        return True
    
    def start_backend(self):
        """Start the FastAPI backend server."""
        print_colored("🚀 Starting backend server (FastAPI)...", Colors.OKBLUE)
        
        try:
            # Use uv to run the FastAPI server
            self.backend_process = subprocess.Popen(
                ["uv", "run", "fastapi", "run", "app", "--port", "8001", "--reload"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Start thread to monitor backend output
            backend_thread = threading.Thread(
                target=self.monitor_process,
                args=(self.backend_process, "BACKEND", Colors.OKGREEN),
                daemon=True
            )
            backend_thread.start()
            
            print_colored("✅ Backend server started on http://localhost:8001", Colors.OKGREEN)
            return True
            
        except Exception as e:
            print_colored(f"❌ Failed to start backend: {e}", Colors.FAIL)
            return False
    
    def start_frontend(self):
        """Start the Next.js frontend server."""
        print_colored("🚀 Starting frontend server (Next.js)...", Colors.OKBLUE)
        
        try:
            frontend_dir = Path("frontend")
            
            # Start the Next.js development server
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Start thread to monitor frontend output
            frontend_thread = threading.Thread(
                target=self.monitor_process,
                args=(self.frontend_process, "FRONTEND", Colors.OKCYAN),
                daemon=True
            )
            frontend_thread.start()
            
            print_colored("✅ Frontend server started on http://localhost:3000", Colors.OKGREEN)
            return True
            
        except Exception as e:
            print_colored(f"❌ Failed to start frontend: {e}", Colors.FAIL)
            return False
    
    def monitor_process(self, process, name, color):
        """Monitor process output and print with colors."""
        try:
            for line in iter(process.stdout.readline, ''):
                if line and self.running:
                    # Filter out some verbose logs
                    if any(skip in line.lower() for skip in ['watching for', 'compiled', 'hot reload']):
                        continue
                    
                    timestamp = time.strftime("%H:%M:%S")
                    print_colored(f"[{timestamp}] {name}: {line.strip()}", color)
        except Exception:
            pass
    
    def wait_for_servers(self):
        """Wait for servers to be ready."""
        print_colored("⏳ Waiting for servers to be ready...", Colors.WARNING)
        
        # Wait a bit for servers to start
        time.sleep(3)
        
        # Check backend health
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print_colored("✅ Backend is ready", Colors.OKGREEN)
            else:
                print_colored("⚠️  Backend may not be fully ready", Colors.WARNING)
        except Exception:
            print_colored("⚠️  Backend health check failed (this is normal during startup)", Colors.WARNING)
        
        print_colored("🎉 Application is starting up!", Colors.OKGREEN)
        print()
        print_colored("📱 Frontend: http://localhost:3000", Colors.OKBLUE)
        print_colored("🔧 Backend API: http://localhost:8000", Colors.OKBLUE)
        print_colored("📚 API Docs: http://localhost:8000/docs", Colors.OKBLUE)
        print_colored("🤖 Agent Health: http://localhost:8000/v1/agents/health", Colors.OKBLUE)
        print()
        print_colored("Press Ctrl+C to stop all services", Colors.WARNING)
    
    def stop_all(self):
        """Stop all running processes."""
        self.running = False
        
        if self.backend_process:
            print_colored("🛑 Stopping backend server...", Colors.WARNING)
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
        
        if self.frontend_process:
            print_colored("🛑 Stopping frontend server...", Colors.WARNING)
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
        
        print_colored("✅ All services stopped", Colors.OKGREEN)
    
    def run(self):
        """Main run method."""
        print_header()
        
        # Check dependencies
        if not self.check_dependencies():
            sys.exit(1)
        
        print()
        
        # Start backend
        if not self.start_backend():
            sys.exit(1)
        
        # Wait a moment for backend to start
        time.sleep(2)
        
        # Start frontend
        if not self.start_frontend():
            self.stop_all()
            sys.exit(1)
        
        # Wait for servers to be ready
        self.wait_for_servers()
        
        # Keep the main thread alive
        try:
            while self.running:
                time.sleep(1)
                
                # Check if processes are still running
                if self.backend_process and self.backend_process.poll() is not None:
                    print_colored("❌ Backend process died unexpectedly", Colors.FAIL)
                    break
                
                if self.frontend_process and self.frontend_process.poll() is not None:
                    print_colored("❌ Frontend process died unexpectedly", Colors.FAIL)
                    break
                    
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_all()

def main():
    """Main entry point."""
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Create and run process manager
    manager = ProcessManager()
    manager.run()

if __name__ == "__main__":
    main()
