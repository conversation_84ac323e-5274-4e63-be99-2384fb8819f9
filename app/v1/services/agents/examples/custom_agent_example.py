# app/v1/services/agents/examples/custom_agent_example.py

"""
Example of how to create a custom agent for the multi-agent system.

This example shows how to:
1. Create a new agent by extending BaseAgent
2. Implement the required _process_query method
3. Register the agent with the system
4. Handle errors and provide appropriate responses
"""

import time
from typing import List, Dict, Any
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

from app.shared.database.models import UserTenantDB
from app.shared.config.agent_config import agent_config
from ..base_agent import BaseAgent
from ..types import AgentResponse, AgentContext, AgentType, AgentStatus, SourceReference
from ..registry import agent_registry

class ComplianceAgent(BaseAgent):
    """
    Example custom agent for legal compliance checking.
    
    This agent specializes in:
    - Checking legal compliance requirements
    - Analyzing regulatory frameworks
    - Providing compliance recommendations
    """
    
    def __init__(self):
        super().__init__(
            name="compliance_checker",
            agent_type=AgentType.GENERAL_CHAT,  # You can define new types in types.py
            timeout_seconds=agent_config.AGENT_DEFAULT_TIMEOUT,
            max_retries=agent_config.AGENT_MAX_RETRIES
        )
        
        # Initialize LLM for this agent
        self.llm = ChatOpenAI(
            model=agent_config.AGENT_LLM_MODEL,
            temperature=0.1,  # Low temperature for compliance accuracy
            max_tokens=agent_config.AGENT_LLM_MAX_TOKENS
        )
        
        # Define compliance-related keywords
        self.compliance_keywords = [
            "अनुपालन", "नियमावली", "आवश्यकता", "कानुनी बाध्यता",
            "compliance", "regulation", "requirement", "mandatory"
        ]
    
    async def _process_query(self, context: AgentContext) -> AgentResponse:
        """
        Process a compliance-related query.
        
        Args:
            context: The context containing user query and metadata
            
        Returns:
            AgentResponse: Response with compliance analysis
        """
        start_time = time.time()
        
        try:
            # Check if this is actually a compliance query
            if not self._is_compliance_query(context.user_query):
                return AgentResponse(
                    agent_type=self.agent_type,
                    agent_name=self.name,
                    status=AgentStatus.SUCCESS,
                    content="यो प्रश्न अनुपालन सम्बन्धी देखिदैन। कृपया अनुपालन वा नियमावली सम्बन्धी प्रश्न सोध्नुहोस्।",
                    processing_time_ms=(time.time() - start_time) * 1000,
                    metadata={"query_type": "non_compliance"}
                )
            
            # Create system prompt for compliance analysis
            system_prompt = """तपाईं एक कानुनी अनुपालन विशेषज्ञ हुनुहुन्छ। तपाईंको काम:
1. कानुनी अनुपालन आवश्यकताहरू विश्लेषण गर्नु
2. नियमावली र बाध्यताहरू बुझाउनु
3. अनुपालन सुझावहरू प्रदान गर्नु
4. जोखिम मूल्याङ्कन गर्नु

सधैं नेपाली भाषामा जवाफ दिनुहोस् र स्पष्ट, व्यावहारिक सल्लाह दिनुहोस्।"""
            
            # Generate response using LLM
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=context.user_query)
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Create mock sources (in a real implementation, you'd search compliance databases)
            sources = self._generate_compliance_sources(context.user_query)
            
            processing_time = (time.time() - start_time) * 1000
            
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.SUCCESS,
                content=response.content,
                sources=sources,
                metadata={
                    "query_type": "compliance",
                    "compliance_keywords_found": self._extract_keywords(context.user_query),
                    "risk_level": self._assess_risk_level(context.user_query)
                },
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Error in compliance agent: {e}")
            
            processing_time = (time.time() - start_time) * 1000
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.ERROR,
                content="माफ गर्नुहोस्, अनुपालन विश्लेषण गर्दा समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                processing_time_ms=processing_time,
                error_message=str(e)
            )
    
    def _is_compliance_query(self, query: str) -> bool:
        """Check if the query is related to compliance."""
        query_lower = query.lower()
        return any(keyword in query_lower for keyword in self.compliance_keywords)
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Extract compliance keywords from the query."""
        query_lower = query.lower()
        found_keywords = []
        
        for keyword in self.compliance_keywords:
            if keyword in query_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _assess_risk_level(self, query: str) -> str:
        """Assess the risk level based on the query content."""
        high_risk_terms = ["दण्ड", "जरिवाना", "कारबाही", "penalty", "fine", "violation"]
        medium_risk_terms = ["आवश्यक", "बाध्यकारी", "mandatory", "required"]
        
        query_lower = query.lower()
        
        if any(term in query_lower for term in high_risk_terms):
            return "high"
        elif any(term in query_lower for term in medium_risk_terms):
            return "medium"
        else:
            return "low"
    
    def _generate_compliance_sources(self, query: str) -> List[SourceReference]:
        """Generate mock compliance sources (replace with real database search)."""
        # In a real implementation, you would search compliance databases
        mock_sources = [
            SourceReference(
                document_id="compliance_001",
                filename="नेपाल सरकार नियमावली २०७९.pdf",
                page_number=15,
                text_snippet="अनुपालन आवश्यकताहरू र बाध्यताहरू...",
                confidence_score=0.85
            ),
            SourceReference(
                document_id="compliance_002", 
                filename="कानुनी अनुपालन गाइडलाइन.pdf",
                page_number=8,
                text_snippet="नियमावली पालना र जोखिम व्यवस्थापन...",
                confidence_score=0.78
            )
        ]
        
        return mock_sources
    
    def get_capabilities(self) -> List[str]:
        """Get list of capabilities this agent provides."""
        return [
            "compliance_analysis",
            "regulatory_guidance",
            "risk_assessment",
            "compliance_recommendations",
            "legal_obligations_review"
        ]

# Example of how to register the agent
def register_compliance_agent():
    """Register the compliance agent with the system."""
    try:
        compliance_agent = ComplianceAgent()
        agent_registry.register_agent(compliance_agent)
        print(f"✅ Registered compliance agent: {compliance_agent.name}")
        return True
    except Exception as e:
        print(f"❌ Failed to register compliance agent: {e}")
        return False

# Example usage in your application startup
if __name__ == "__main__":
    # This is how you would register the agent during application startup
    register_compliance_agent()
    
    # You can also check if it's registered
    agent = agent_registry.get_agent("compliance_checker")
    if agent:
        print(f"Agent info: {agent.get_info()}")
        print(f"Capabilities: {agent.get_capabilities()}")

"""
To add this agent to your application:

1. Create your agent file in app/v1/services/agents/
2. Import and register it in app/v1/services/agents/service.py in the _initialize_agents method:

   from .compliance_agent import ComplianceAgent
   
   def _initialize_agents(self):
       # ... existing agents ...
       
       # Register compliance agent
       compliance_agent = ComplianceAgent()
       agent_registry.register_agent(compliance_agent)

3. Update the orchestrator routing logic if needed to route compliance queries to this agent

4. Add any new agent types to types.py if you need them:

   class AgentType(str, Enum):
       ORCHESTRATOR = "orchestrator"
       DOCUMENT_SEARCH = "document_search"
       GENERAL_CHAT = "general_chat"
       COMPLIANCE = "compliance"  # New type

5. Test your agent using the API:

   POST /v1/agents/chat
   {
       "query": "कानुनी अनुपालन आवश्यकताहरू के के छन्?",
       "agent_name": "compliance_checker"  # Optional: force use of specific agent
   }
"""
