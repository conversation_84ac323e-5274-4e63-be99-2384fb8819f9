# app/v1/services/chat/service.py

import time
import uuid
from typing import List, Dict, Optional
from fastapi import HTT<PERSON>Exception
from datetime import datetime

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.storage.minio_client import Min<PERSON><PERSON><PERSON>, MinIOConfig
from app.v1.services.knowledge_base.service import KnowledgeBaseService
from .models import ChatResponse, SourceReference, ChatHistory, ConversationSummary

logger = setup_new_logging(__name__)

class ChatService:
    """
    Standalone chat service for handling AI-powered conversations with document retrieval.
    """

    @staticmethod
    async def process_chat_query(
        query: str, 
        current_user: UserTenantDB, 
        max_results: int = 5,
        conversation_id: Optional[str] = None,
        include_sources: bool = True
    ) -> ChatResponse:
        """
        Process a chat query and return AI response with sources.
        """
        try:
            start_time = time.time()
            
            # Generate conversation ID if not provided
            if not conversation_id:
                conversation_id = str(uuid.uuid4())
            
            # Search for relevant documents using the knowledge base service
            search_results = await KnowledgeBaseService.search_documents(query, current_user, max_results)
            
            # If vector search returns no results, try text search as fallback
            if not search_results:
                search_results = await KnowledgeBaseService.search_documents_by_text(query, current_user, max_results)
            
            # Generate AI response
            ai_response = await ChatService._generate_ai_response(query, search_results, current_user)
            
            # Format source references
            sources = []
            if include_sources:
                sources = await ChatService._format_source_references(search_results, current_user)
            
            processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            # Create response
            response = ChatResponse(
                query=query,
                answer=ai_response,
                sources=sources,
                total_sources_found=len(search_results),
                processing_time_ms=round(processing_time, 2),
                language="nepali",
                conversation_id=conversation_id,
                timestamp=datetime.now()
            )
            
            # Store chat history
            await ChatService._store_chat_history(response, current_user)
            
            logger.info(f"Processed chat query in {processing_time:.2f}ms: {query[:50]}...")
            return response
            
        except Exception as e:
            logger.error(f"Error processing chat query: {e}")
            raise HTTPException(status_code=500, detail=f"Error processing chat query: {str(e)}")

    @staticmethod
    async def _generate_ai_response(query: str, context_chunks: List[dict], current_user: UserTenantDB) -> str:
        """
        Generate AI response using retrieved context chunks.
        For now, this is a simple template-based response.
        In production, you would integrate with an LLM API like OpenAI, Anthropic, or local models.
        """
        try:
            if not context_chunks:
                return "माफ गर्नुहोस्, मैले तपाईंको प्रश्नको उत्तर दिन सक्ने कुनै सान्दर्भिक जानकारी फेला पारेन।"
            
            # Combine context from all chunks
            context_text = ""
            sources = []
            
            for i, chunk in enumerate(context_chunks[:3]):  # Use top 3 chunks
                context_text += f"सन्दर्भ {i+1}: {chunk.get('text', '')}\n\n"
                sources.append({
                    "filename": chunk.get('filename', 'Unknown'),
                    "page": chunk.get('page_number', 'Unknown')
                })
            
            # Simple template-based response (replace with actual LLM integration)
            response = f"""तपाईंको प्रश्न: {query}

उपलब्ध कागजातहरूको आधारमा:

{context_text}

उत्तर: उपरोक्त सन्दर्भको आधारमा, यो जानकारी {', '.join([f"{s['filename']} (पृष्ठ {s['page']})" for s in sources])} मा फेला परेको छ।

कृपया थप विस्तृत जानकारीको लागि मूल कागजातहरू हेर्नुहोस्।"""

            logger.info(f"Generated AI response for query: {query[:50]}...")
            return response
            
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return "माफ गर्नुहोस्, उत्तर उत्पादन गर्दा त्रुटि भयो। कृपया फेरि प्रयास गर्नुहोस्।"

    @staticmethod
    async def _format_source_references(search_results: List[dict], current_user: UserTenantDB) -> List[SourceReference]:
        """
        Format search results into source references with fresh presigned URLs and enhanced attribution.
        """
        sources = []

        for result in search_results:
            # Generate fresh presigned URL if available
            presigned_url = result.get('presigned_url')
            if result.get('minio_path') and not presigned_url:
                try:
                    # Get MinIO configuration
                    minio_config = await current_user.get_minio_config()

                    # Initialize MinIO client
                    minio_client = MinIOClient(MinIOConfig(
                        minio_url=minio_config["minio_url"],
                        access_key=minio_config["access_key"],
                        secret_key=minio_config["secret_key"],
                        bucket_name=minio_config["bucket_name"],
                        secure=minio_config["secure"]
                    ))

                    # Extract folder and filename from minio_path
                    minio_path = result["minio_path"]
                    if "/" in minio_path:
                        folder = "/".join(minio_path.split("/")[:-1])
                        filename = minio_path.split("/")[-1]
                    else:
                        folder = None
                        filename = minio_path

                    # Generate fresh presigned URL
                    presigned_url = minio_client.get_presigned_url(
                        object_name=filename,
                        folder=folder,
                        expiry_hours=24
                    )

                except Exception as e:
                    logger.warning(f"Could not generate presigned URL: {e}")

            # Get enhanced page coordinates and attribution
            coordinates = await ChatService._get_source_coordinates(result, current_user)

            source = SourceReference(
                document_id=result.get('document_id', ''),
                filename=result.get('filename', ''),
                page_number=result.get('page_number', 0),
                chunk_id=result.get('chunk_id', ''),
                text_snippet=result.get('text', '')[:200] + "..." if len(result.get('text', '')) > 200 else result.get('text', ''),
                confidence_score=result.get('score', 0.0),
                minio_path=result.get('minio_path'),
                presigned_url=presigned_url,
                coordinates=coordinates
            )
            sources.append(source)

        return sources

    @staticmethod
    async def _get_source_coordinates(result: dict, current_user: UserTenantDB) -> Optional[Dict]:
        """
        Get detailed coordinates and positioning information for a source reference.
        """
        try:
            from app.v1.services.knowledge_base.service import KnowledgeBaseService

            document_id = result.get('document_id')
            page_number = result.get('page_number')
            chunk_text = result.get('text', '')

            if not document_id or not page_number:
                return None

            # Get page information with text blocks
            try:
                page_info = await KnowledgeBaseService.get_page_info(document_id, page_number, current_user)
            except Exception:
                return None

            text_blocks = page_info.get('text_blocks', [])
            page_dimensions = page_info.get('page_dimensions', {})

            # Find the text block that best matches our chunk
            best_match = None
            best_score = 0

            for block in text_blocks:
                block_text = block.get('text', '')
                if block_text and chunk_text:
                    # Calculate similarity between chunk and block
                    chunk_words = set(chunk_text.lower().split())
                    block_words = set(block_text.lower().split())

                    if chunk_words and block_words:
                        intersection = chunk_words & block_words
                        union = chunk_words | block_words
                        similarity = len(intersection) / len(union) if union else 0

                        if similarity > best_score:
                            best_score = similarity
                            best_match = block

            if best_match and best_score > 0.3:  # Minimum similarity threshold
                return {
                    "page_number": page_number,
                    "bbox": best_match.get('bbox', []),
                    "x": best_match.get('x', 0),
                    "y": best_match.get('y', 0),
                    "width": best_match.get('width', 0),
                    "height": best_match.get('height', 0),
                    "page_width": page_dimensions.get('width', 0),
                    "page_height": page_dimensions.get('height', 0),
                    "similarity_score": best_score,
                    "block_text": best_match.get('text', '')[:100] + "..." if len(best_match.get('text', '')) > 100 else best_match.get('text', '')
                }

            return None

        except Exception as e:
            logger.warning(f"Could not get source coordinates: {e}")
            return None

    @staticmethod
    async def _store_chat_history(response: ChatResponse, current_user: UserTenantDB):
        """
        Store chat history in MongoDB.
        """
        try:
            chat_collection = current_user.adb.chat_history
            
            chat_record = ChatHistory(
                conversation_id=response.conversation_id,
                user_id=current_user.id,
                tenant_id=current_user.tenant_id,
                query=response.query,
                response=response.answer,
                sources=response.sources,
                timestamp=response.timestamp,
                processing_time_ms=response.processing_time_ms
            )
            
            await chat_collection.insert_one(chat_record.dict())
            logger.debug(f"Stored chat history for conversation {response.conversation_id}")
            
        except Exception as e:
            logger.error(f"Error storing chat history: {e}")
            # Don't raise exception as this is not critical for the response

    @staticmethod
    async def get_chat_history(
        current_user: UserTenantDB, 
        conversation_id: Optional[str] = None,
        limit: int = 50,
        skip: int = 0
    ) -> List[ChatHistory]:
        """
        Get chat history for a user or specific conversation.
        """
        try:
            chat_collection = current_user.adb.chat_history
            
            query_filter = {"user_id": current_user.id, "tenant_id": current_user.tenant_id}
            if conversation_id:
                query_filter["conversation_id"] = conversation_id
            
            cursor = chat_collection.find(query_filter).sort("timestamp", -1).skip(skip).limit(limit)
            history = await cursor.to_list(length=limit)
            
            # Convert to ChatHistory objects
            chat_history = []
            for record in history:
                record["_id"] = str(record["_id"])  # Convert ObjectId to string
                chat_history.append(ChatHistory(**record))
            
            return chat_history
            
        except Exception as e:
            logger.error(f"Error getting chat history: {e}")
            raise HTTPException(status_code=500, detail="Error retrieving chat history")

    @staticmethod
    async def get_conversations(current_user: UserTenantDB, limit: int = 20) -> List[ConversationSummary]:
        """
        Get conversation summaries for a user.
        """
        try:
            chat_collection = current_user.adb.chat_history
            
            # Aggregate conversations
            pipeline = [
                {"$match": {"user_id": current_user.id, "tenant_id": current_user.tenant_id}},
                {"$sort": {"timestamp": -1}},
                {"$group": {
                    "_id": "$conversation_id",
                    "title": {"$first": "$query"},
                    "last_message": {"$first": "$response"},
                    "message_count": {"$sum": 1},
                    "created_at": {"$min": "$timestamp"},
                    "updated_at": {"$max": "$timestamp"}
                }},
                {"$sort": {"updated_at": -1}},
                {"$limit": limit}
            ]
            
            conversations = []
            async for doc in chat_collection.aggregate(pipeline):
                summary = ConversationSummary(
                    conversation_id=doc["_id"],
                    title=doc["title"][:50] + "..." if len(doc["title"]) > 50 else doc["title"],
                    last_message=doc["last_message"][:100] + "..." if len(doc["last_message"]) > 100 else doc["last_message"],
                    message_count=doc["message_count"],
                    created_at=doc["created_at"],
                    updated_at=doc["updated_at"]
                )
                conversations.append(summary)
            
            return conversations
            
        except Exception as e:
            logger.error(f"Error getting conversations: {e}")
            raise HTTPException(status_code=500, detail="Error retrieving conversations")

    @staticmethod
    async def create_source_attribution_map(
        document_id: str,
        current_user: UserTenantDB
    ) -> dict:
        """
        Create a comprehensive source attribution map for a document.
        This maps chunks to their exact locations in the PDF.
        """
        try:
            from app.v1.services.knowledge_base.service import KnowledgeBaseService

            # Get all pages for the document
            pages = await KnowledgeBaseService.get_document_pages(document_id, current_user)

            # Get collection settings to find chunks
            collection_settings = await KnowledgeBaseService._get_collection_settings(current_user)
            split_docs_collection = collection_settings["split_docs_collection"]

            # Initialize Qdrant to get chunks
            qdrant_client = await current_user.init_qdrant()

            # Get all chunks for this document from Qdrant
            # Note: This is a simplified approach - in production you'd use scroll API for large datasets
            search_results = await qdrant_client.search(
                collection_name=split_docs_collection,
                query_vector=[0.0] * 1536,  # Dummy vector
                limit=1000,  # Large limit to get all chunks
                with_payload=True,
                with_vectors=False
            )

            # Filter chunks for this document
            document_chunks = []
            for result in search_results:
                metadata = result.payload.get("metadata", {})
                if metadata.get("parent_doc_id") == document_id:
                    document_chunks.append({
                        "id": result.id,
                        "text": result.payload.get("text", ""),
                        "metadata": metadata,
                        "chunk_id": metadata.get("chunk_id", ""),
                        "page_number": metadata.get("page_number", 1),
                        "chunk_index": metadata.get("chunk_index", 0)
                    })

            # Create attribution map
            attribution_map = {
                "document_id": document_id,
                "total_pages": len(pages),
                "total_chunks": len(document_chunks),
                "page_mappings": {},
                "chunk_mappings": {}
            }

            # Map chunks to pages with coordinates
            for chunk in document_chunks:
                page_number = chunk["page_number"]
                chunk_id = chunk["chunk_id"]
                chunk_text = chunk["text"]

                # Find the corresponding page
                page_info = next((p for p in pages if p["page_number"] == page_number), None)

                if page_info:
                    # Find best matching text block
                    text_blocks = page_info.get("text_blocks", [])
                    best_block = None
                    best_score = 0

                    for block in text_blocks:
                        block_text = block.get("text", "")
                        if block_text and chunk_text:
                            # Calculate text similarity
                            chunk_words = set(chunk_text.lower().split())
                            block_words = set(block_text.lower().split())

                            if chunk_words and block_words:
                                intersection = chunk_words & block_words
                                union = chunk_words | block_words
                                similarity = len(intersection) / len(union) if union else 0

                                if similarity > best_score:
                                    best_score = similarity
                                    best_block = block

                    # Store chunk mapping
                    chunk_mapping = {
                        "chunk_id": chunk_id,
                        "chunk_index": chunk["chunk_index"],
                        "page_number": page_number,
                        "text_preview": chunk_text[:100] + "..." if len(chunk_text) > 100 else chunk_text,
                        "coordinates": None,
                        "similarity_score": best_score
                    }

                    if best_block and best_score > 0.3:
                        chunk_mapping["coordinates"] = {
                            "bbox": best_block.get("bbox", []),
                            "x": best_block.get("x", 0),
                            "y": best_block.get("y", 0),
                            "width": best_block.get("width", 0),
                            "height": best_block.get("height", 0)
                        }

                    attribution_map["chunk_mappings"][chunk_id] = chunk_mapping

                    # Add to page mappings
                    if page_number not in attribution_map["page_mappings"]:
                        attribution_map["page_mappings"][page_number] = {
                            "page_number": page_number,
                            "chunks": [],
                            "page_dimensions": page_info.get("page_dimensions", {}),
                            "total_text_blocks": len(text_blocks)
                        }

                    attribution_map["page_mappings"][page_number]["chunks"].append(chunk_id)

            logger.info(f"Created source attribution map for document {document_id} with {len(document_chunks)} chunks")
            return attribution_map

        except Exception as e:
            logger.error(f"Error creating source attribution map: {e}")
            raise HTTPException(status_code=500, detail="Error creating source attribution map")

    @staticmethod
    async def get_chunk_location(
        chunk_id: str,
        current_user: UserTenantDB
    ) -> dict:
        """
        Get the exact location of a specific chunk in its source document.
        """
        try:
            from app.v1.services.knowledge_base.service import KnowledgeBaseService

            # Get collection settings
            collection_settings = await KnowledgeBaseService._get_collection_settings(current_user)
            split_docs_collection = collection_settings["split_docs_collection"]

            # Initialize Qdrant
            qdrant_client = await current_user.init_qdrant()

            # Search for the specific chunk
            # Note: In production, you'd store chunk IDs as point IDs for direct retrieval
            search_results = await qdrant_client.search(
                collection_name=split_docs_collection,
                query_vector=[0.0] * 1536,  # Dummy vector
                limit=100,
                with_payload=True,
                with_vectors=False
            )

            # Find the chunk
            target_chunk = None
            for result in search_results:
                metadata = result.payload.get("metadata", {})
                if metadata.get("chunk_id") == chunk_id:
                    target_chunk = {
                        "id": result.id,
                        "text": result.payload.get("text", ""),
                        "metadata": metadata
                    }
                    break

            if not target_chunk:
                raise HTTPException(status_code=404, detail="Chunk not found")

            # Get coordinates using the existing method
            coordinates = await ChatService._get_source_coordinates(target_chunk, current_user)

            return {
                "chunk_id": chunk_id,
                "document_id": target_chunk["metadata"].get("parent_doc_id", ""),
                "page_number": target_chunk["metadata"].get("page_number", 1),
                "chunk_index": target_chunk["metadata"].get("chunk_index", 0),
                "text_preview": target_chunk["text"][:200] + "..." if len(target_chunk["text"]) > 200 else target_chunk["text"],
                "coordinates": coordinates,
                "filename": target_chunk["metadata"].get("filename", "")
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting chunk location: {e}")
            raise HTTPException(status_code=500, detail="Error getting chunk location")
