# app/v1/services/chat/models.py

from pydantic import BaseModel
from typing import List, Optional, Dict
from datetime import datetime

class ChatQuery(BaseModel):
    """
    Chat query request model.
    """
    query: str
    max_results: Optional[int] = 5
    include_sources: Optional[bool] = True
    language: Optional[str] = "nepali"
    conversation_id: Optional[str] = None

class SourceReference(BaseModel):
    """
    Source reference model for chat responses.
    """
    document_id: str
    filename: str
    page_number: int
    chunk_id: Optional[str] = None
    text_snippet: str
    confidence_score: Optional[float] = None
    minio_path: Optional[str] = None
    presigned_url: Optional[str] = None
    coordinates: Optional[Dict] = None  # For future PDF coordinate tracking

class ChatResponse(BaseModel):
    """
    Chat response model with AI answer and sources.
    """
    query: str
    answer: str
    sources: List[SourceReference]
    total_sources_found: int
    processing_time_ms: Optional[float] = None
    language: str = "nepali"
    conversation_id: Optional[str] = None
    timestamp: datetime = datetime.now()

class DocumentSearchResult(BaseModel):
    """
    Document search result model.
    """
    document_id: str
    filename: str
    relevance_score: float
    text_snippet: str
    page_number: Optional[int] = None
    chunk_id: Optional[str] = None
    metadata: Optional[Dict] = None

class ChatHistory(BaseModel):
    """
    Chat history model for storing conversations.
    """
    conversation_id: str
    user_id: str
    tenant_id: str
    query: str
    response: str
    sources: List[SourceReference]
    timestamp: datetime
    processing_time_ms: float

class ConversationSummary(BaseModel):
    """
    Conversation summary model.
    """
    conversation_id: str
    title: str
    last_message: str
    message_count: int
    created_at: datetime
    updated_at: datetime
