# app/v1/services/chat/uniform_formatter.py

from typing import List, Dict, Optional
from collections import defaultdict
import time
from app.v1.services.chat.models import SentenceData, SourceGroup, UniformResponse
from app.shared.database.models import UserTenantDB
from app.shared.storage.minio_client import MinIOClient, MinIOConfig
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class UniformResponseFormatter:
    """
    Utility class for formatting responses into the uniform format.
    Groups data by PDF source with sentences containing sent_id, content, and page numbers.
    """
    
    @staticmethod
    async def format_search_results(
        search_results: List[dict], 
        current_user: UserTenantDB,
        processing_time_ms: Optional[float] = None
    ) -> UniformResponse:
        """
        Format search results from knowledge base into uniform response format.
        Groups results by PDF source (filename) and creates sentence arrays.
        
        Args:
            search_results: List of search result dictionaries from knowledge base
            current_user: Current user context for MinIO access
            processing_time_ms: Optional processing time
            
        Returns:
            UniformResponse: Formatted response grouped by PDF source
        """
        try:
            # Group results by source (filename)
            grouped_sources = defaultdict(list)
            
            for result in search_results:
                filename = result.get('filename', 'unknown.pdf')
                
                # Extract sentence data
                sentence_data = SentenceData(
                    sentence=result.get('text', ''),
                    page_number=result.get('page_number', 0),
                    sent_id=result.get('id', ''),  # Use node ID as sent_id
                    confidence_score=result.get('score', 0.0)
                )
                
                grouped_sources[filename].append({
                    'sentence_data': sentence_data,
                    'minio_path': result.get('minio_path'),
                    'metadata': result.get('metadata', {})
                })
            
            # Create source groups
            source_groups = []
            for filename, sentences_info in grouped_sources.items():
                # Get MinIO path and presigned URL from first sentence (they should be the same for all sentences from same PDF)
                first_sentence_info = sentences_info[0]
                minio_path = first_sentence_info['minio_path']
                presigned_url = None
                
                # Generate presigned URL if MinIO path exists
                if minio_path:
                    presigned_url = await UniformResponseFormatter._generate_presigned_url(
                        minio_path, current_user
                    )
                
                # Extract sentence data
                sentences = [info['sentence_data'] for info in sentences_info]
                
                source_group = SourceGroup(
                    source_name=filename,
                    sentences=sentences,
                    minio_path=minio_path,
                    presigned_url=presigned_url
                )
                source_groups.append(source_group)
            
            # Create uniform response
            uniform_response = UniformResponse(
                sources=source_groups,
                total_sources_found=len(search_results),
                processing_time_ms=processing_time_ms
            )
            
            logger.info(f"Formatted {len(search_results)} results into {len(source_groups)} source groups")
            return uniform_response
            
        except Exception as e:
            logger.error(f"Error formatting search results: {e}")
            # Return empty response on error
            return UniformResponse(
                sources=[],
                total_sources_found=0,
                processing_time_ms=processing_time_ms
            )
    
    @staticmethod
    async def format_agent_sources(
        agent_sources: List[dict],
        current_user: UserTenantDB,
        processing_time_ms: Optional[float] = None
    ) -> UniformResponse:
        """
        Format agent response sources into uniform format.
        
        Args:
            agent_sources: List of source dictionaries from agent response
            current_user: Current user context
            processing_time_ms: Optional processing time
            
        Returns:
            UniformResponse: Formatted response grouped by PDF source
        """
        # Agent sources might have different structure, adapt as needed
        return await UniformResponseFormatter.format_search_results(
            agent_sources, current_user, processing_time_ms
        )
    
    @staticmethod
    async def _generate_presigned_url(minio_path: str, current_user: UserTenantDB) -> Optional[str]:
        """
        Generate presigned URL for MinIO object.
        
        Args:
            minio_path: Path to object in MinIO
            current_user: Current user context
            
        Returns:
            Optional[str]: Presigned URL or None if generation fails
        """
        try:
            # Get MinIO configuration
            minio_config = await current_user.get_minio_config()
            
            # Initialize MinIO client
            minio_client = MinIOClient(MinIOConfig(
                minio_url=minio_config["minio_url"],
                access_key=minio_config["access_key"],
                secret_key=minio_config["secret_key"],
                bucket_name=minio_config["bucket_name"],
                secure=minio_config["secure"]
            ))
            
            # Extract folder and filename from minio_path
            if "/" in minio_path:
                folder = "/".join(minio_path.split("/")[:-1])
                filename = minio_path.split("/")[-1]
            else:
                folder = None
                filename = minio_path
            
            # Generate presigned URL (24 hour expiry)
            presigned_url = minio_client.get_presigned_url(
                object_name=filename,
                folder=folder,
                expiry_hours=24
            )
            
            return presigned_url
            
        except Exception as e:
            logger.warning(f"Could not generate presigned URL for {minio_path}: {e}")
            return None
    
    @staticmethod
    def convert_to_legacy_format(uniform_response: UniformResponse) -> List[Dict]:
        """
        Convert uniform response format back to legacy format for backward compatibility.
        
        Args:
            uniform_response: UniformResponse object
            
        Returns:
            List[Dict]: Legacy format source references
        """
        legacy_sources = []
        
        for source_group in uniform_response.sources:
            for sentence in source_group.sentences:
                legacy_source = {
                    'document_id': sentence.sent_id,  # Use sent_id as document_id
                    'filename': source_group.source_name,
                    'page_number': sentence.page_number,
                    'chunk_id': sentence.sent_id,
                    'text': sentence.sentence,
                    'score': sentence.confidence_score or 0.0,
                    'minio_path': source_group.minio_path,
                    'presigned_url': source_group.presigned_url
                }
                legacy_sources.append(legacy_source)
        
        return legacy_sources
