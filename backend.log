
   FastAPI   Starting production server 🚀
 
             Searching for package file structure from directories with         
             __init__.py files                                                  
             Importing from /home/<USER>/Documents/Nextai/legal
 
    module   📁 app            
             └── 🐍 __init__.py
 
      code   Importing the FastAPI app object from the module with the following
             code:                                                              
 
             from app import app
 
       app   Using import string: app:app
 
    server   Server started at http://0.0.0.0:8000
    server   Documentation at http://0.0.0.0:8000/docs
 
             Logs:
 
      INFO   Will watch for changes in these directories:                       
             ['/home/<USER>/Documents/Nextai/legal']
      INFO   Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
      INFO   Started reloader process [51630] using WatchFiles
      INFO   Started server process [51661]
      INFO   Waiting for application startup.
2025-07-20 11:54:37,305 - app - INFO - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-20 11:54:37,309 - app - WARNING - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-20 11:54:37,309 - app - INFO - Application startup completed
      INFO   Application startup complete.
2025-07-20 11:54:42,765 - app.v1.services.auth.routes - INFO - Processing login with client_id: legal
2025-07-20 11:54:42,765 - app.v1.services.auth.routes - INFO - Processing login with client_id: legal
2025-07-20 11:54:42,835 - app.v1.services.auth.service - INFO - Token validity settings: {'_id': ObjectId('6878828a49f6ba511d84e11a'), 'name': 'token_validity', 'days': 0, 'hours': 6, 'minutes': 0, 'seconds': 0}
2025-07-20 11:54:42,835 - app.v1.services.auth.service - INFO - Token validity settings: {'_id': ObjectId('6878828a49f6ba511d84e11a'), 'name': 'token_validity', 'days': 0, 'hours': 6, 'minutes': 0, 'seconds': 0}
      INFO   127.0.0.1:37070 - "POST /v1/auth/login HTTP/1.1" 200
      INFO   127.0.0.1:37076 - "OPTIONS                                         
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
2025-07-20 11:54:49,100 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
2025-07-20 11:54:49,100 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
      INFO   127.0.0.1:37076 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
2025-07-20 11:54:49,265 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
2025-07-20 11:54:49,265 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
      INFO   127.0.0.1:37076 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:44502 - "OPTIONS /v1/agents/chat HTTP/1.1" 200
2025-07-20 11:55:02,893 - app.v1.services.agents.routes - INFO - Processing agent chat request from user 6878828a49f6ba511d84e11b
2025-07-20 11:55:02,893 - app.v1.services.agents.routes - INFO - Processing agent chat request from user 6878828a49f6ba511d84e11b
2025-07-20 11:55:02,924 - app.v1.services.agents.memory.session_memory - INFO - Created MongoDB indexes for session memory
2025-07-20 11:55:02,924 - app.v1.services.agents.memory.session_memory - INFO - Created MongoDB indexes for session memory
2025-07-20 11:55:02,924 - app.v1.services.agents.memory.session_memory - INFO - Initialized session memory manager: legal_agent_memory.agent_sessions
2025-07-20 11:55:02,924 - app.v1.services.agents.memory.session_memory - INFO - Initialized session memory manager: legal_agent_memory.agent_sessions
2025-07-20 11:55:02,942 - app.v1.services.agents.memory.user_context - INFO - Created MongoDB indexes for user context
2025-07-20 11:55:02,942 - app.v1.services.agents.memory.user_context - INFO - Created MongoDB indexes for user context
2025-07-20 11:55:02,942 - app.v1.services.agents.memory.user_context - INFO - Initialized user context manager: legal_agent_memory.user_contexts
2025-07-20 11:55:02,942 - app.v1.services.agents.memory.user_context - INFO - Initialized user context manager: legal_agent_memory.user_contexts
2025-07-20 11:55:02,947 - app.v1.services.agents.base_agent.document_search - INFO - Initialized LangGraph for agent document_search
2025-07-20 11:55:02,947 - app.v1.services.agents.base_agent.document_search - INFO - Initialized LangGraph for agent document_search
2025-07-20 11:55:02,947 - app.v1.services.agents.base_agent.document_search - INFO - Initialized LangGraph for agent document_search
2025-07-20 11:55:02,947 - app.v1.services.agents.registry.AgentRegistry - INFO - Registered agent: document_search (type: AgentType.DOCUMENT_SEARCH)
2025-07-20 11:55:02,947 - app.v1.services.agents.registry.AgentRegistry - INFO - Registered agent: document_search (type: AgentType.DOCUMENT_SEARCH)
2025-07-20 11:55:02,947 - app.v1.services.agents.registry.AgentRegistry - INFO - Registered agent: document_search (type: AgentType.DOCUMENT_SEARCH)
2025-07-20 11:55:02,949 - app.v1.services.agents.base_agent.orchestrator - INFO - Initialized LangGraph for agent orchestrator
2025-07-20 11:55:02,949 - app.v1.services.agents.base_agent.orchestrator - INFO - Initialized LangGraph for agent orchestrator
2025-07-20 11:55:02,949 - app.v1.services.agents.base_agent.orchestrator - INFO - Initialized LangGraph for agent orchestrator
2025-07-20 11:55:03,115 - app.v1.services.agents.registry.AgentRegistry - INFO - Registered agent: orchestrator (type: AgentType.ORCHESTRATOR)
2025-07-20 11:55:03,115 - app.v1.services.agents.registry.AgentRegistry - INFO - Registered agent: orchestrator (type: AgentType.ORCHESTRATOR)
2025-07-20 11:55:03,115 - app.v1.services.agents.registry.AgentRegistry - INFO - Registered agent: orchestrator (type: AgentType.ORCHESTRATOR)
2025-07-20 11:55:03,115 - app.v1.services.agents.service - INFO - Agent system initialized successfully
2025-07-20 11:55:03,115 - app.v1.services.agents.service - INFO - Agent system initialized successfully
2025-07-20 11:55:03,116 - app.v1.services.agents.memory.session_memory - INFO - Created session a0eda90a-1664-44f4-9a58-7b981dd5639a for user 6878828a49f6ba511d84e11b
2025-07-20 11:55:03,116 - app.v1.services.agents.memory.session_memory - INFO - Created session a0eda90a-1664-44f4-9a58-7b981dd5639a for user 6878828a49f6ba511d84e11b
2025-07-20 11:55:03,116 - app.v1.services.agents.base_agent.orchestrator - INFO - Running agent orchestrator for query: when was प्रतिनिधि सभा र प्रदेश सभा...
2025-07-20 11:55:03,116 - app.v1.services.agents.base_agent.orchestrator - INFO - Running agent orchestrator for query: when was प्रतिनिधि सभा र प्रदेश सभा...
2025-07-20 11:55:03,116 - app.v1.services.agents.base_agent.orchestrator - INFO - Running agent orchestrator for query: when was प्रतिनिधि सभा र प्रदेश सभा...
2025-07-20 11:55:06,457 - app.v1.services.agents.memory.user_context - ERROR - Failed to update user context for 6878828a49f6ba511d84e11b: update only works with $ operators
2025-07-20 11:55:06,457 - app.v1.services.agents.memory.user_context - ERROR - Failed to update user context for 6878828a49f6ba511d84e11b: update only works with $ operators
      INFO   127.0.0.1:44502 - "POST /v1/agents/chat HTTP/1.1" 200
      INFO   127.0.0.1:44502 - "OPTIONS                                         
             /v1/knowledge-base/search_all?query=when+was+%E0%A4%AA%E0%A5%8D%E0%
             A4%B0%E0%A4%A4%E0%A4%BF%E0%A4%A8%E0%A4%BF%E0%A4%A7%E0%A4%BF+%E0%A4%
             B8%E0%A4%AD%E0%A4%BE+%E0%A4%B0+%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%A6
             %E0%A5%87%E0%A4%B6+%E0%A4%B8%E0%A4%AD%E0%A4%BE&page=1&page_size=12 
             HTTP/1.1" 200
2025-07-20 11:55:06,621 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
2025-07-20 11:55:06,621 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
      INFO   127.0.0.1:44502 - "GET                                             
             /v1/knowledge-base/search_all?query=when+was+%E0%A4%AA%E0%A5%8D%E0%
             A4%B0%E0%A4%A4%E0%A4%BF%E0%A4%A8%E0%A4%BF%E0%A4%A7%E0%A4%BF+%E0%A4%
             B8%E0%A4%AD%E0%A4%BE+%E0%A4%B0+%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%A6
             %E0%A5%87%E0%A4%B6+%E0%A4%B8%E0%A4%AD%E0%A4%BE&page=1&page_size=12 
             HTTP/1.1" 200
2025-07-20 11:55:55,021 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
2025-07-20 11:55:55,021 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
      INFO   127.0.0.1:56092 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
2025-07-20 11:55:55,197 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
2025-07-20 11:55:55,197 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
      INFO   127.0.0.1:56092 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:56092 - "OPTIONS                                         
             /v1/knowledge-base/query?query=when+was+%E0%A4%AA%E0%A5%8D%E0%A4%B0
             %E0%A4%A4%E0%A4%BF%E0%A4%A8%E0%A4%BF%E0%A4%A7%E0%A4%BF+%E0%A4%B8%E0
             %A4%AD%E0%A4%BE+%E0%A4%B0+%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%A6%E0%A
             5%87%E0%A4%B6+%E0%A4%B8%E0%A4%AD%E0%A4%BE HTTP/1.1" 200
2025-07-20 11:55:59,066 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
2025-07-20 11:55:59,066 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
2025-07-20 11:56:02,172 - app.v1.services.knowledge_base.service - INFO - Processed query with engine: when was प्रतिनिधि सभा र प्रदेश सभा
2025-07-20 11:56:02,172 - app.v1.services.knowledge_base.service - INFO - Processed query with engine: when was प्रतिनिधि सभा र प्रदेश सभा
      INFO   127.0.0.1:56092 - "GET                                             
             /v1/knowledge-base/query?query=when+was+%E0%A4%AA%E0%A5%8D%E0%A4%B0
             %E0%A4%A4%E0%A4%BF%E0%A4%A8%E0%A4%BF%E0%A4%A7%E0%A4%BF+%E0%A4%B8%E0
             %A4%AD%E0%A4%BE+%E0%A4%B0+%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%A6%E0%A
             5%87%E0%A4%B6+%E0%A4%B8%E0%A4%AD%E0%A4%BE HTTP/1.1" 200
2025-07-20 11:56:02,322 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
2025-07-20 11:56:02,322 - app.v1.services.knowledge_base.service - INFO - Initialized KnowledgeBaseService with OpenAI embeddings for collection: bibtest_sentence_context
      INFO   127.0.0.1:56092 - "GET                                             
             /v1/knowledge-base/search_all?query=when+was+%E0%A4%AA%E0%A5%8D%E0%
             A4%B0%E0%A4%A4%E0%A4%BF%E0%A4%A8%E0%A4%BF%E0%A4%A7%E0%A4%BF+%E0%A4%
             B8%E0%A4%AD%E0%A4%BE+%E0%A4%B0+%E0%A4%AA%E0%A5%8D%E0%A4%B0%E0%A4%A6
             %E0%A5%87%E0%A4%B6+%E0%A4%B8%E0%A4%AD%E0%A4%BE&page=1&page_size=12 
             HTTP/1.1" 200
